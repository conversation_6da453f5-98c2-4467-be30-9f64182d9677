/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.command.commands

import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import net.ccbluex.liquidbounce.LiquidBounce
import net.ccbluex.liquidbounce.LiquidBounce.moduleManager
import net.ccbluex.liquidbounce.api.ClientApi
import net.ccbluex.liquidbounce.api.Status
import net.ccbluex.liquidbounce.api.autoSettingsList
import net.ccbluex.liquidbounce.api.loadSettings
import net.ccbluex.liquidbounce.config.SettingsUtils
import net.ccbluex.liquidbounce.features.command.Command
import net.ccbluex.liquidbounce.file.FileManager.settingsDir
import net.ccbluex.liquidbounce.ui.client.hud.HUD.addNotification
import net.ccbluex.liquidbounce.ui.client.hud.element.elements.Notification
import net.ccbluex.liquidbounce.utils.client.ClientUtils.LOGGER
import net.ccbluex.liquidbounce.utils.io.MiscUtils
import net.ccbluex.liquidbounce.utils.io.json
import net.ccbluex.liquidbounce.utils.io.readJson
import net.ccbluex.liquidbounce.utils.io.writeJson
import net.ccbluex.liquidbounce.utils.kotlin.SharedScopes
import net.ccbluex.liquidbounce.utils.kotlin.StringUtils
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.time.LocalDate

object SettingsCommand : Command("autosettings", "autosetting", "settings", "setting", "config") {

    private val mutex = Mutex()

    /**
     * Execute commands with provided [args]
     */
    override fun execute(args: Array<String>) {
        val usedAlias = args[0].lowercase()

        if (args.size <= 1) {
            chatSyntax("$usedAlias <load/save/list/upload/report>")
            return
        }

        if (mutex.isLocked) {
            chat("§cPrevious task is not finished!")
            return
        }

        SharedScopes.IO.launch {
            mutex.withLock {
                when (args[1].lowercase()) {
                    "load" -> loadSettings(args)
                    "save" -> saveSettings(args)
                    "report" -> reportSettings(args)
                    "upload" -> uploadSettings(args)
                    "list" -> listSettings()
                    else -> chatSyntax("$usedAlias <load/save/list/upload/report>")
                }
            }
        }
    }

    // Load subcommand
    private suspend fun loadSettings(args: Array<String>) {
        if (args.size < 3) {
            chatSyntax("${args[0].lowercase()} load <name/url>")
            return
        }

        try {
            val input = args[2]

            // Check if it's a local JSON file
            if (!input.startsWith("http") && !input.contains("/")) {
                val jsonFile = File(settingsDir, "$input.json")
                if (jsonFile.exists()) {
                    withContext(Dispatchers.IO) {
                        loadJsonConfig(jsonFile)
                    }
                    return
                }
            }

            // Load from URL or script
            val settings = SettingsUtils.loadFromUrl(input)

            chat("Applying settings...")
            SettingsUtils.applyScript(settings)
            chat("§6Settings applied successfully")
            addNotification(Notification("Settings Command", "Successfully updated settings!"))
            playEdit()
        } catch (e: Exception) {
            LOGGER.error("Failed to load settings", e)
            chat("Failed to load settings: ${e.message}")
        }
    }

    // Save subcommand
    private suspend fun saveSettings(args: Array<String>) {
        withContext(Dispatchers.IO) {
            if (args.size < 3) {
                chatSyntax("${args[0].lowercase()} save <name>")
                return@withContext
            }

            val configName = args[2]
            val configFile = File(settingsDir, "$configName.json")

            try {
                chat("§9Creating configuration...")

                val jsonConfig = json {
                    "ConfigName" to configName
                    "ClientVersion" to LiquidBounce.clientVersionText
                    "CreatedDate" to LocalDate.now().toString()
                    "Modules" to json {
                        for (module in moduleManager) {
                            module.name to json {
                                "State" to module.state
                                "KeyBind" to module.keyBind
                                "Values" to json {
                                    for (value in module.values) {
                                        if (value.shouldRender()) {
                                            value.name to value.toJson()
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                chat("§9Saving configuration...")
                configFile.writeJson(jsonConfig)

                chat("§6Configuration saved successfully as §a$configName.json")
                addNotification(Notification("Settings Command", "Configuration saved successfully!"))
                playEdit()

            } catch (e: Exception) {
                LOGGER.error("Failed to save configuration", e)
                chat("§cFailed to save configuration: ${e.message}")
            }
        }
    }

    // Load JSON config helper
    private fun loadJsonConfig(configFile: File) {
        try {
            chat("§9Loading JSON configuration...")

            val json = configFile.readJson() as? JsonObject ?: run {
                chat("§cInvalid JSON configuration file!")
                return
            }

            val configName = json["ConfigName"]?.asString ?: "Unknown"
            val clientVersion = json["ClientVersion"]?.asString ?: "Unknown"

            chat("§9Loading configuration: §a$configName §9(Version: §a$clientVersion§9)")

            val modulesJson = json["Modules"]?.asJsonObject ?: run {
                chat("§cNo modules found in configuration!")
                return
            }

            var loadedModules = 0

            for ((moduleName, moduleData) in modulesJson.entrySet()) {
                val module = moduleManager[moduleName] ?: continue
                val moduleJson = moduleData.asJsonObject

                // Load module state
                moduleJson["State"]?.let {
                    module.state = it.asBoolean
                }

                // Load key bind
                moduleJson["KeyBind"]?.let {
                    module.keyBind = it.asInt
                }

                // Load module values
                moduleJson["Values"]?.asJsonObject?.let { valuesJson ->
                    for ((valueName, valueData) in valuesJson.entrySet()) {
                        val moduleValue = module[valueName]
                        if (moduleValue != null) {
                            try {
                                moduleValue.fromJson(valueData)
                            } catch (e: Exception) {
                                LOGGER.warn("Failed to load value $valueName for module $moduleName: ${e.message}")
                            }
                        }
                    }
                }

                loadedModules++
            }

            chat("§6Configuration loaded successfully! §9($loadedModules modules)")
            addNotification(Notification("Settings Command", "Configuration loaded successfully!"))
            playEdit()

        } catch (e: Exception) {
            LOGGER.error("Failed to load JSON configuration", e)
            chat("§cFailed to load configuration: ${e.message}")
        }
    }

    // Report subcommand
    private fun reportSettings(args: Array<String>) {
        if (args.size < 3) {
            chatSyntax("${args[0].lowercase()} report <name>")
            return
        }

        try {
            val response = runBlocking { ClientApi.reportSettings(settingId = args[2]) }
            when (response.status) {
                Status.SUCCESS -> chat("§6${response.message}")
                Status.ERROR -> chat("§c${response.message}")
            }
        } catch (e: Exception) {
            LOGGER.error("Failed to report settings", e)
            chat("Failed to report settings: ${e.message}")
        }
    }

    // Upload subcommand
    private fun uploadSettings(args: Array<String>) {
        val option = if (args.size > 3) StringUtils.toCompleteString(args, 3).lowercase() else "all"
        val all = "all" in option
        val values = all || "values" in option
        val binds = all || "binds" in option
        val states = all || "states" in option

        if (!values && !binds && !states) {
            chatSyntax("${args[0].lowercase()} upload [all/values/binds/states]...")
            return
        }

        try {
            chat("§9Creating settings...")
            val settingsScript = SettingsUtils.generateScript(values, binds, states)
            chat("§9Uploading settings...")

            val serverData = mc.currentServerData ?: error("You need to be on a server to upload settings.")

            val name = "${LiquidBounce.clientCommit}-${serverData.serverIP.replace(".", "_")}"
            val response = runBlocking {
                ClientApi.uploadSettings(
                    name = name.toRequestBody(),
                    contributors = mc.session.username.toRequestBody(),
                    settingsFile = MultipartBody.Part.createFormData(
                        "settings_file",
                        "settings_file",
                        settingsScript.toByteArray().toRequestBody("application/octet-stream".toMediaTypeOrNull())
                    )
                )
            }

            when (response.status) {
                Status.SUCCESS -> {
                    chat("§6${response.message}")
                    chat("§9Token: §6${response.token}")

                    // Store token in clipboard
                    MiscUtils.copy(response.token)
                }

                Status.ERROR -> chat("§c${response.message}")
            }
        } catch (e: Exception) {
            LOGGER.error("Failed to upload settings", e)
            chat("Failed to upload settings: ${e.message}")
        }
    }

    // List subcommand
    private fun listSettings() {
        chat("Loading settings...")
        loadSettings(false) {
            for (setting in it) {
                chat("> ${setting.settingId} (Last updated: ${setting.date}, Status: ${setting.statusType.displayName})")
            }
        }
    }

    override fun tabComplete(args: Array<String>): List<String> {
        if (args.isEmpty()) {
            return emptyList()
        }

        return when (args.size) {
            1 -> listOf("list", "load", "save", "upload", "report").filter { it.startsWith(args[0], true) }
            2 -> {
                when (args[0].lowercase()) {
                    "load", "report" -> {
                        if (autoSettingsList == null) {
                            loadSettings(true, 500)
                        }

                        return autoSettingsList?.filter { it.settingId.startsWith(args[1], true) }?.map { it.settingId }
                            ?: emptyList()
                    }

                    "save" -> {
                        // Suggest existing config names for saving
                        val configs = settingsDir.listFiles()?.filter { it.extension == "json" }?.map { it.nameWithoutExtension } ?: emptyList()
                        return configs.filter { it.startsWith(args[1], true) }
                    }

                    "upload" -> {
                        return listOf("all", "values", "binds", "states").filter { it.startsWith(args[1], true) }
                    }

                    else -> emptyList()
                }
            }

            else -> emptyList()
        }
    }
}
